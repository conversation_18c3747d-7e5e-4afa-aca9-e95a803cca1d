#include <algorithm>
#include "processing_helpers.h"

constexpr double MIN_LINE_RATE_HZ = 15000.0;    // Minimum line rate for video processing (auto)
constexpr int LINE_PER_CHUNK = 4;               // Number of lines per chunk for processing

namespace ProcessingHelpers {

size_t calculateSliceSize(const uint32_t sampleRate, const std::string& strategy, const size_t defaultSize) {
    constexpr int linesPerChunk = 4;
    double lineRate = MIN_LINE_RATE_HZ;
    if (strategy == "auto_ntsc") {
        // Use NTSC line rate if available
        lineRate = 15734.0; // NTSC line rate in Hz
    }
    else if (strategy == "auto_pal") {
        // Use PAL line rate if available
        lineRate = 15625.0; // PAL line rate in Hz
    }

    const auto samplesPerLine = static_cast<size_t>(sampleRate / lineRate);
    return samplesPerLine * linesPerChunk;
}

AcquisitionConfig AcquisitionConfig::calculateConfig(uint32_t queueDepthRaw) {
    AcquisitionConfig config;

    // Use conservative defaults to avoid assertion failures
    config.writeChunkSize = 1024;   // Smaller write chunk size
    config.readChunkSize = 1024;    // Same as write chunk size for simplicity
    config.readOverlapSize = 256;   // 25% overlap
    config.numWriteChunks = std::max(queueDepthRaw, static_cast<uint32_t>(4));
    config.sampleRate = 20000000;   // Default 20 MS/s (will be overridden)
    config.sliceStrategy = "fixed"; // Use fixed strategy to avoid auto calculation issues

    return config;
}

} // namespace ProcessingHelpers

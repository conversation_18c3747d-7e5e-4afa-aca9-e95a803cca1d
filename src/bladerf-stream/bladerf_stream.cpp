#include "bladerf_stream.h"
#include <libbladeRF.h>
#include <stdexcept>
#include <cstring>
#include <algorithm>

const std::string BladeRFStream::sourceName_ = "bladeRF";

// Ring buffer implementation
BladeRFStream::RingBuffer::RingBuffer(size_t capacity) 
    : capacity_(capacity), writePos_(0), readPos_(0) {
    buffer_ = std::make_unique<uint32_t[]>(capacity_);
}

BladeRFStream::RingBuffer::~RingBuffer() = default;

size_t BladeRFStream::RingBuffer::push(const uint32_t* samples, size_t count) {
    size_t writePos = writePos_.load(std::memory_order_relaxed);
    size_t readPos = readPos_.load(std::memory_order_acquire);
    
    // Calculate available space
    size_t available = (readPos <= writePos) ? 
        (capacity_ - writePos + readPos - 1) : 
        (readPos - writePos - 1);
    
    size_t toPush = std::min(count, available);
    if (toPush == 0) return 0;
    
    // Handle wrap-around
    size_t firstChunk = std::min(toPush, capacity_ - writePos);
    std::memcpy(buffer_.get() + writePos, samples, firstChunk * sizeof(uint32_t));
    
    if (toPush > firstChunk) {
        std::memcpy(buffer_.get(), samples + firstChunk, (toPush - firstChunk) * sizeof(uint32_t));
    }
    
    writePos_.store((writePos + toPush) % capacity_, std::memory_order_release);
    return toPush;
}

size_t BladeRFStream::RingBuffer::pop(uint32_t* samples, size_t count) {
    size_t readPos = readPos_.load(std::memory_order_relaxed);
    size_t writePos = writePos_.load(std::memory_order_acquire);
    
    // Calculate available data
    size_t available = (writePos >= readPos) ? 
        (writePos - readPos) : 
        (capacity_ - readPos + writePos);
    
    size_t toPop = std::min(count, available);
    if (toPop == 0) return 0;
    
    // Handle wrap-around
    size_t firstChunk = std::min(toPop, capacity_ - readPos);
    std::memcpy(samples, buffer_.get() + readPos, firstChunk * sizeof(uint32_t));
    
    if (toPop > firstChunk) {
        std::memcpy(samples + firstChunk, buffer_.get(), (toPop - firstChunk) * sizeof(uint32_t));
    }
    
    readPos_.store((readPos + toPop) % capacity_, std::memory_order_release);
    return toPop;
}

size_t BladeRFStream::RingBuffer::available() const {
    size_t readPos = readPos_.load(std::memory_order_relaxed);
    size_t writePos = writePos_.load(std::memory_order_acquire);
    
    return (writePos >= readPos) ? 
        (writePos - readPos) : 
        (capacity_ - readPos + writePos);
}

size_t BladeRFStream::RingBuffer::capacity() const {
    return capacity_;
}

// BladeRF stream implementation
BladeRFStream::BladeRFStream(const std::string& serial, 
                             uint32_t channel, 
                             uint32_t sampleRate,
                             double centerHz, 
                             double bandwidth)
    : dev_(nullptr), stream_(nullptr), fs_(sampleRate), channel_(channel), 
      serial_(serial), active_(false) {
    
    try {
        // Open device
        const char* serial_ptr = serial.empty() ? nullptr : serial.c_str();
        int status = bladerf_open(&dev_, serial_ptr);
        if (status != 0) {
            err_ = "Failed to open BladeRF device: " + std::string(bladerf_strerror(status));
            throw std::runtime_error(err_);
        }
        
        // Configure device
        configureDevice(channel, sampleRate, centerHz, bandwidth);

        // Create ring buffer (1 second of samples)
        size_t bufferSize = sampleRate;
        ringBuffer_ = std::make_unique<RingBuffer>(bufferSize);

        // Configure sync interface
        status = bladerf_sync_config(dev_, BLADERF_RX_X1, BLADERF_FORMAT_SC16_Q11,
                                    64, 8192, 8, 3500);
        if (status != 0) {
            err_ = "Failed to configure sync interface: " + std::string(bladerf_strerror(status));
            throw std::runtime_error(err_);
        }
        
        active_ = true;
        
    } catch (...) {
        if (dev_) {
            bladerf_close(dev_);
            dev_ = nullptr;
        }
        throw;
    }
}

BladeRFStream::~BladeRFStream() {
    close();
}

void BladeRFStream::configureDevice(uint32_t channel, uint32_t sampleRate, double centerHz, double bandwidth) {
    int status;
    
    // Set sample rate
    uint32_t actual_rate;
    status = bladerf_set_sample_rate(dev_, BLADERF_CHANNEL_RX(channel), sampleRate, &actual_rate);
    if (status != 0) {
        err_ = "Failed to set sample rate: " + std::string(bladerf_strerror(status));
        throw std::runtime_error(err_);
    }
    
    // Set frequency
    status = bladerf_set_frequency(dev_, BLADERF_CHANNEL_RX(channel), static_cast<uint64_t>(centerHz));
    if (status != 0) {
        err_ = "Failed to set frequency: " + std::string(bladerf_strerror(status));
        throw std::runtime_error(err_);
    }
    
    // Set bandwidth
    uint32_t actual_bw;
    status = bladerf_set_bandwidth(dev_, BLADERF_CHANNEL_RX(channel), static_cast<uint32_t>(bandwidth), &actual_bw);
    if (status != 0) {
        err_ = "Failed to set bandwidth: " + std::string(bladerf_strerror(status));
        throw std::runtime_error(err_);
    }
    
    // Set gain to a reasonable default
    status = bladerf_set_gain(dev_, BLADERF_CHANNEL_RX(channel), 30);
    if (status != 0) {
        err_ = "Failed to set gain: " + std::string(bladerf_strerror(status));
        throw std::runtime_error(err_);
    }
    
    // Enable RX channel
    status = bladerf_enable_module(dev_, BLADERF_CHANNEL_RX(channel), true);
    if (status != 0) {
        err_ = "Failed to enable RX channel: " + std::string(bladerf_strerror(status));
        throw std::runtime_error(err_);
    }
}

void BladeRFStream::startAsyncRX() {
    // For now, use synchronous RX instead of async streaming
    // This simplifies the implementation for the initial stages
    // TODO: Implement proper async streaming in future iterations
}

void BladeRFStream::stopAsyncRX() {
    if (stream_) {
        bladerf_deinit_stream(stream_);
        stream_ = nullptr;
    }
}

void* BladeRFStream::streamCallback(struct bladerf* dev, struct bladerf_stream* stream,
                                   struct bladerf_metadata* meta, void* samples,
                                   size_t num_samples, void* user_data) {
    auto* self = static_cast<BladeRFStream*>(user_data);
    if (self && self->ringBuffer_) {
        self->ringBuffer_->push(static_cast<const uint32_t*>(samples), num_samples);
    }
    return samples;  // Return the buffer for reuse
}

bool BladeRFStream::readSamples(SampleType* dst, size_t sampleCount) {
    if (!active_) {
        return false;
    }

    // For now, use synchronous reading
    // TODO: Implement proper async reading from ring buffer
    int status = bladerf_sync_rx(dev_, dst, sampleCount, nullptr, 5000);
    if (status != 0) {
        err_ = "Failed to read samples: " + std::string(bladerf_strerror(status));
        return false;
    }

    return true;
}

uint32_t BladeRFStream::sampleRate() const noexcept {
    return fs_;
}

const std::string& BladeRFStream::sourceName() const noexcept {
    return sourceName_;
}

bool BladeRFStream::isActive() const noexcept {
    return active_.load();
}

void BladeRFStream::close() noexcept {
    if (active_.exchange(false)) {
        stopAsyncRX();
        
        if (dev_) {
            bladerf_enable_module(dev_, BLADERF_CHANNEL_RX(channel_), false);
            bladerf_close(dev_);
            dev_ = nullptr;
        }
    }
}

const std::string& BladeRFStream::lastError() const noexcept {
    return err_;
}

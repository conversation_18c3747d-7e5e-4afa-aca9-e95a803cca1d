#pragma once

#include <string>

namespace ProcessingHelpers {

/**
 * Calculate read chunk size based on slice strategy
 * @param sampleRate Sample rate in Hz
 * @param strategy Slice strategy string ("fixed", "auto_ntsc")
 * @param defaultSize Default size to use for "fixed" strategy
 * @return Calculated chunk size in samples
 */
size_t calculateSliceSize(uint32_t sampleRate, const std::string& strategy, size_t defaultSize);

/**
 * Configuration for acquisition processing
 */
struct AcquisitionConfig {
    size_t writeChunkSize;      // Size of each write chunk in samples
    size_t readChunkSize;       // Size of each read chunk in samples
    size_t readOverlapSize;     // Overlap between read chunks in samples
    size_t numWriteChunks;      // Number of write chunks in buffer
    uint32_t sampleRate;        // Sample rate in Hz
    std::string sliceStrategy;  // Slice strategy ("fixed", "auto_ntsc")
    
    /**
     * Calculate acquisition configuration from queue depth
     * @param queueDepthRaw Raw sample queue depth
     * @return Calculated acquisition configuration
     */
    static AcquisitionConfig calculateConfig(uint32_t queueDepthRaw);
};

} // namespace ProcessingHelpers

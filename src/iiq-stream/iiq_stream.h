#ifndef IIQ_STREAM_H
#define IIQ_STREAM_H

#include <string>
#include "../types.h"

/**
 * IIQStream - Abstract interface for IQ data streaming sources
 * 
 * Provides a unified interface for reading IQ samples from various sources
 * such as WAV files, BladeRF devices, or other SDR hardware.
 * 
 * IQ samples are packed as 32-bit little-endian words (uint32_t 0xQQQQIIII)
 * where the lower 16 bits are I (in-phase) and upper 16 bits are Q (quadrature).
 */
class IIQStream {
public:
    /**
     * Read exactly sampleCount IQ pairs (interleaved I,Q) into dst.
     * @param dst Destination buffer for IQ samples
     * @param sampleCount Number of IQ pairs to read
     * @return true on success, false on EOS; throws on fatal error
     */
    virtual bool readSamples(SampleType* dst, size_t sampleCount) = 0;
    
    /**
     * Get the sample rate in Hz
     * @return Sample rate in Hz
     */
    virtual uint32_t sampleRate() const noexcept = 0;
    
    /**
     * Get the source name identifier
     * @return Source name (e.g., "wav", "bladeRF")
     */
    virtual const std::string& sourceName() const noexcept = 0;
    
    /**
     * Check if the stream is active (not EOS and not closed)
     * @return true if active, false otherwise
     */
    virtual bool isActive() const noexcept = 0;
    
    /**
     * Close the stream (idempotent operation)
     */
    virtual void close() noexcept = 0;
    
    /**
     * Get the last error message
     * @return Last error message or empty string if no error
     */
    virtual const std::string& lastError() const noexcept = 0;
    
    /**
     * Virtual destructor
     */
    virtual ~IIQStream() = default;
};

#endif // IIQ_STREAM_H

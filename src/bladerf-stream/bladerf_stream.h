#ifndef BLADERF_STREAM_H
#define BLADERF_STREAM_H

#include "../iiq-stream/iiq_stream.h"
#include <string>
#include <memory>
#include <atomic>
#include <mutex>
#include <condition_variable>
#include <thread>

// Include BladeRF headers
#include <libbladeRF.h>

/**
 * BladeRFStream - IQ data streaming from BladeRF devices
 * 
 * Implements the IIQStream interface for BladeRF SDR devices.
 * Uses asynchronous streaming with a lock-free ring buffer for high-performance data transfer.
 */
class BladeRFStream final : public IIQStream {
public:
    /**
     * Constructor
     * @param serial Device serial number (empty for first available)
     * @param channel Channel number (typically 0)
     * @param sampleRate Sample rate in Hz
     * @param centerHz Center frequency in Hz
     * @param bandwidth Bandwidth in Hz
     */
    BladeRFStream(const std::string& serial, 
                  uint32_t channel, 
                  uint32_t sampleRate,
                  double centerHz, 
                  double bandwidth);
    
    /**
     * Destructor
     */
    ~BladeRFStream() override;
    
    // IIQStream interface implementation
    bool readSamples(SampleType* dst, size_t sampleCount) override;
    uint32_t sampleRate() const noexcept override;
    const std::string& sourceName() const noexcept override;
    bool isActive() const noexcept override;
    void close() noexcept override;
    const std::string& lastError() const noexcept override;

private:
    // BladeRF device handle
    struct bladerf* dev_;
    struct bladerf_stream* stream_;
    
    // Configuration
    uint32_t fs_;
    uint32_t channel_;
    std::string serial_;
    
    // State management
    std::atomic<bool> active_;
    std::string err_;
    
    // Ring buffer for async data transfer
    class RingBuffer;
    std::unique_ptr<RingBuffer> ringBuffer_;
    
    // Source name constant
    static const std::string sourceName_;
    
    /**
     * Configure BladeRF device with specified parameters
     */
    void configureDevice(uint32_t channel, uint32_t sampleRate, double centerHz, double bandwidth);
    
    /**
     * Start asynchronous RX streaming
     */
    void startAsyncRX();
    
    /**
     * Stop asynchronous RX streaming
     */
    void stopAsyncRX();
    
    /**
     * BladeRF stream callback function
     */
    static void* streamCallback(struct bladerf* dev, struct bladerf_stream* stream,
                               struct bladerf_metadata* meta, void* samples,
                               size_t num_samples, void* user_data);
    
    // Disable copy constructor and assignment
    BladeRFStream(const BladeRFStream&) = delete;
    BladeRFStream& operator=(const BladeRFStream&) = delete;
};

/**
 * Lock-free SPSC ring buffer for uint32_t samples
 */
class BladeRFStream::RingBuffer {
public:
    explicit RingBuffer(size_t capacity);
    ~RingBuffer();
    
    /**
     * Push samples into the buffer (producer side)
     * @param samples Pointer to samples
     * @param count Number of samples
     * @return Number of samples actually pushed
     */
    size_t push(const uint32_t* samples, size_t count);
    
    /**
     * Pop samples from the buffer (consumer side)
     * @param samples Destination buffer
     * @param count Number of samples requested
     * @return Number of samples actually popped
     */
    size_t pop(uint32_t* samples, size_t count);
    
    /**
     * Get number of available samples for reading
     */
    size_t available() const;
    
    /**
     * Get buffer capacity
     */
    size_t capacity() const;

private:
    std::unique_ptr<uint32_t[]> buffer_;
    size_t capacity_;
    std::atomic<size_t> writePos_;
    std::atomic<size_t> readPos_;
    
    // Disable copy constructor and assignment
    RingBuffer(const RingBuffer&) = delete;
    RingBuffer& operator=(const RingBuffer&) = delete;
};

#endif // BLADERF_STREAM_H

#ifndef CHUNK_PROCESSOR_HPP
#define CHUNK_PROCESSOR_HPP

#include <cstdint>
#include <cstddef>
#include <cassert>
#include <cstring>
#include <functional>
#include <memory>

/**
 * ChunkProcessor - High-performance template-based ring buffer for overlapping read windows
 *
 * Template-based implementation supporting any sample type with comprehensive test coverage
 *
 * Features:
 * - Template-based design supporting any sample type (defaults to uint32_t)
 * - Zero-copy strategy with minimal data copying
 * - Single-producer/single-consumer ring buffer
 * - Overlapping read windows with configurable overlap
 * - Synchronous callback execution
 * - Header-only implementation for optimal performance
 *
 * Usage:
 *   ChunkProcessor<uint32_t> processor(writeChunkSize, readChunkSize, readOverlapSize,
 *                                     numOfWriteChunks, readChunkHandler);
 *   // Or simply:
 *   ChunkProcessor processor(writeChunkSize, readChunkSize, readOverlapSize,
 *                           numOfWriteChunks, readChunkHandler);  // defaults to uint32_t
 *
 *   // Write data in two steps:
 *   SampleType* ptr = processor.getWriteChunkPtr();
 *   // ... fill ptr with data ...
 *   processor.commitWriteChunk();  // Triggers read callbacks if enough data available
 */
template<typename SampleType = uint32_t>
class ChunkProcessor {
public:
    using ReadHandler = std::function<void(SampleType*, size_t)>;

    /**
     * Constructor
     * 
     * @param writeChunkSize Size (in samples) of each write operation
     * @param readChunkSize Size (in samples) of each read window
     * @param readOverlapSize Overlap between consecutive read windows
     * @param numOfWriteChunks Number of write chunks that fit into the internal buffer
     * @param readChunkHandler Callback fired synchronously when enough data is available
     */
    ChunkProcessor(size_t writeChunkSize,
                   size_t readChunkSize,
                   size_t readOverlapSize,
                   size_t numOfWriteChunks,
                   ReadHandler readChunkHandler)
        : wc(writeChunkSize),
          rc(readChunkSize),
          ov(readOverlapSize),
          stride(rc - ov),
          nwc(numOfWriteChunks),
          handler(std::move(readChunkHandler)),
          mainBufSamples(wc * nwc),
          mainBuffer(new SampleType[mainBufSamples]),
          glueBuffer(new SampleType[rc]),
          writeHeadAbs(0),
          nextReadAbs(0)
    {
        // Construction-time assertions as per specification
        assert(wc > 0 && rc > 0 && nwc >= 2);
        assert(ov < rc);
        assert(mainBufSamples >= rc + ov);
    }

    /**
     * Get pointer to next free write chunk
     *
     * @return Pointer to write location for next chunk
     */
    SampleType* getWriteChunkPtr() noexcept {
        return mainBuffer.get() + (writeHeadAbs % mainBufSamples);
    }

    /**
     * Commit the write chunk and trigger read callbacks if enough data is available
     * 
     * This method advances the write head and checks if any read windows can be satisfied.
     * For each available read window, it calls the readChunkHandler callback synchronously.
     * 
     * When a read window would wrap around the end of the buffer, the data is copied
     * into the pre-allocated glueBuffer to provide a contiguous memory region.
     */
    void commitWriteChunk() noexcept {
        writeHeadAbs += wc;
        
        // Process all available read windows
        while (writeHeadAbs - nextReadAbs >= rc) {
            size_t start = nextReadAbs % mainBufSamples;
            SampleType* out;

            if (start + rc <= mainBufSamples) {
                // Zero-copy case: read window fits within buffer bounds
                out = mainBuffer.get() + start;
            } else {
                // Wrap-around case: copy data into glue buffer for contiguous access
                size_t first  = mainBufSamples - start;
                size_t second = rc - first;
                std::memcpy(glueBuffer.get(),            mainBuffer.get() + start,  first  * sizeof(SampleType));
                std::memcpy(glueBuffer.get() + first,    mainBuffer.get(),          second * sizeof(SampleType));
                out = glueBuffer.get();
            }
            
            // Invoke the read handler synchronously
            handler(out, rc);
            
            // Advance to next read window
            nextReadAbs += stride;
        }
    }

    // Disable copy constructor and assignment operator
    ChunkProcessor(const ChunkProcessor&)            = delete;
    ChunkProcessor& operator=(const ChunkProcessor&) = delete;

private:
    // Configuration parameters (immutable after construction)
    const size_t wc;        // writeChunkSize
    const size_t rc;        // readChunkSize  
    const size_t ov;        // readOverlapSize
    const size_t stride;    // readChunkSize - readOverlapSize
    const size_t nwc;       // numOfWriteChunks
    
    // Read handler callback
    ReadHandler handler;

    // Buffer management
    const size_t                       mainBufSamples;  // Total samples in main buffer
    std::unique_ptr<SampleType[]>      mainBuffer;      // Main ring buffer
    std::unique_ptr<SampleType[]>      glueBuffer;      // Buffer for wrap-around cases

    // State tracking (64-bit counters to avoid overflow)
    uint64_t writeHeadAbs;  // Absolute sample index after latest committed write
    uint64_t nextReadAbs;   // Absolute start index of next read window
};

#endif // CHUNK_PROCESSOR_HPP
